<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanTargetsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzPlanTargetsTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="tzPlanTargetsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanTargets"
      row-key="tptId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="tzPlanTargetsEdit">
      <tzPlanTargetsForm
        ref="tzPlanTargetsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="tzPlanTargetsQuerySearch">
      <tzPlanTargetsQuery
        ref="tzPlanTargetsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { tzPlanTargetsDoDeleteELog,
           tzPlanTargetsGetList,
           tzPlanTargetsDoSaveOrUpdLog,
           tzPlanTargetsDoExport } from '@/api/tzgl/investDesign/tzPlanTargets'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import tzPlanTargetsSearch from './components/Search.vue'
  import tzPlanTargetsForm from './components/Form.vue'
  import tzPlanTargetsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanTargets',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      tzPlanTargetsSearch,
      tzPlanTargetsForm,
      tzPlanTargetsQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tptAmount: [
            { required: true, message: '请输入计划投资总金额', trigger: 'blur' }
          ],
          tptAmount0: [
            { required: true, message: '请输入第一年计划投资金额', trigger: 'blur' }
          ],
          tptAmount1: [
            { required: true, message: '请输入第二年计划投资金额', trigger: 'blur' }
          ],
          tptAmount2: [
            { required: true, message: '请输入第三年计划投资金额', trigger: 'blur' }
          ],
          tptAmount3: [
            { required: true, message: '请输入第四年计划投资金额', trigger: 'blur' }
          ],
          tptAmount4: [
            { required: true, message: '请输入第五年计划投资金额', trigger: 'blur' }
          ],
          tptAmount5: [
            { required: true, message: '请输入第六年计划投资金额', trigger: 'blur' }
          ],
          tptDirection: [
            { required: true, message: '请输入重点投资方向', trigger: 'blur' }
          ],
          tptOdId: [
            { required: false, message: '请输入单位ID', trigger: 'blur' }
          ],
          tptOeId: [
            { required: false, message: '请输入操作人员ID', trigger: 'blur' }
          ],
          tptOeName: [
            { required: false, message: '请输入操作人员', trigger: 'blur' }
          ],
          tptSort: [
            { required: true, message: '请输入投资分类', trigger: 'blur' }
          ],
          tptStatus: [
            { required: false, message: '请输入投资指标状态', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['单位','投资分类','重点投资方向','计划投资总金额','第一年计划投资金额','第二年计划投资金额','第三年计划投资金额','第四年计划投资金额','第五年计划投资金额','第六年计划投资金额'],
        columns: [
                { prop:'tptOdName'   , label:'单位', width:'auto'   },
                { prop:'tptSort'     , label:'投资分类', width:'auto'   },
                { prop:'tptDirection', label:'重点投资方向', width:'auto'   },
                { prop:'tptAmount'   , label:'计划投资总金额', width:'auto'   },
                { prop:'tptAmount0'  , label:'第一年计划投资金额', width:'auto'   },
                { prop:'tptAmount1'  , label:'第二年计划投资金额', width:'auto'   },
                { prop:'tptAmount2'  , label:'第三年计划投资金额', width:'auto'   },
                { prop:'tptAmount3'  , label:'第四年计划投资金额', width:'auto'   },
                { prop:'tptAmount4'  , label:'第五年计划投资金额', width:'auto'   },
                { prop:'tptAmount5'  , label:'第六年计划投资金额', width:'auto'   }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          tptType:'投资',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanTargetsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await tzPlanTargetsDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.tzPlanTargetsEdit.close()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={tptType:'投资','tptStatus':'',tptOeName:this.loginUser.oeName,tptOeId:this.loginUser.oeId,tptOdId:this.loginUser.odId}
        this.editType = 'add'
        this.$refs['tzPlanTargetsEdit'].showEdit('添加投资计划')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['tzPlanTargetsEdit'].showEdit('编辑投资计划')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tptId) {
          this.$baseConfirm('确定删除投资计划吗', null, async () => {
            const msg = await tzPlanTargetsDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['tzPlanTargetsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.tzPlanTargetsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.tzPlanTargetsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await tzPlanTargetsGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"createdTime":{"celltype":"date"},"updatedTime":{"celltype":"date"}},
                      "fileName":"投资规划指标.xls",
                      "isnumber":true,
                      "excelTitle":"投资规划指标",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#TzPlanTargets",params)
        const { msg }  =  await tzPlanTargetsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"投资规划指标模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#TzPlanTargets",params)
        const { msg }  =  await tzPlanTargetsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>