<template>
    <el-tabs v-model="activeName" type="card" class="investment-committee-expert">
      <el-tab-pane v-for="(item,index) in tabData" :key="index" :label="item.title" :name="index+''" class="tab-pane">
        <InsideMarket v-if="index != (tabData.length - 1)" :activeName="activeName" :parentData="tabData[index]"/>
        <Contact v-else/>
      </el-tab-pane>
    </el-tabs>
</template>

<script>
  import InsideMarket from './components/InsideMarket';
  import Contact from './components/Contact';

  export default {
    name:'investmentCommitteeExpert',
    components: {
      InsideMarket,
      Contact,
    },
    data(){
      return {
        activeName: '0',
        tabData:[
          {
            title: '内部市场科技专家',
          },
          {
            title: '内部财务投资专家',
          },
          {
            title: '外部市场科技专家',
          },
          {
            title: '外部财务投资专辑',
          },
          {
            title: '联系人',
          },
        ]
      }
    },
  }
</script>

<style lang="scss" scoped>
  .investment-committee-expert{
    background: white;
    .tab-pane{padding: 16px;}

    ::v-deep .el-tabs__nav-scroll{
      background: #f6f8f9;
    }
    ::v-deep .is-active,::v-deep .el-tab-pane{
      background: white;
    }
  }
</style>