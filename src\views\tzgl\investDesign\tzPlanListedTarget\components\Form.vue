<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="200px"
        :model="form"
        :rules="rules">
        <el-col :span="12">
          <el-form-item label="现金分红比率" prop="tpltDividendRatio">
            <el-input-number v-model.trim="form.tpltDividendRatio" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司市值(亿元)" prop="tpltMarketValue">
            <el-input-number v-model.trim="form.tpltMarketValue" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位ID" prop="tpltOdId"  style="display:none;>
            <el-input v-model.trim="form.tpltOdId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作者ID" prop="tpltOeId"  style="display:none;>
            <el-input v-model.trim="form.tpltOeId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作者" prop="tpltOeName"  style="display:none;>
            <el-input v-model.trim="form.tpltOeName" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司基本每股收益(EPS)年增长率" prop="tpltPriceEps">
            <el-input-number v-model.trim="form.tpltPriceEps" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市值排名(申万一级行业或恒生综合行业前%)" prop="tpltPriceRank">
            <el-input-number v-model.trim="form.tpltPriceRank" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="股价相对综合增长率" prop="tpltPriceRate">
            <el-input-number v-model.trim="form.tpltPriceRate" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司市净率(是否破净)" prop="tpltPriceRatio">
            <el-input-number v-model.trim="form.tpltPriceRatio" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构投资者占比" prop="tpltProportion">
            <el-input-number v-model.trim="form.tpltProportion" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证券交易所信息披露质量等级" prop="tpltQualityRating">
            <el-input-number v-model.trim="form.tpltQualityRating" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="tpltStatus"  style="display:none;>
            <el-input v-model.trim="form.tpltStatus" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年度" prop="tpltYear">
            <el-input v-model.trim="form.tpltYear" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="tplt"  style="display:none;">
            <el-input v-model.trim="form.tplt" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanListedTargetForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>