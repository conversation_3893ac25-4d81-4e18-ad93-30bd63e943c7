<template>
  <div class="investment-committee-expert">
    <VabForm :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%"/>
    <VabTable 
      :tableHeader="tableHeader" 
      :tableData="tableData" 
      :tableHeight="tableHeight"
      :pageNo="tablePage.pageNo"
      :pageSize="tablePage.pageSize"
      :total="tablePage.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      v-if="dialogVisible"
      :close="closeDialigCard"
      :title='title'
      :flag="true"
      height="80%"
      width="50%"
      top="15vh">
      <InsideMarketForm slot="content" ref="insideMarketForm" :queryForm="queryForm" :zzmmOptions="zzmmOptions" :qrzOptions="qrzOptions"/>
      <template #footer>
        <el-button @click="closeDialigCard">取消</el-button>
        <el-button type="primary" @click="handleSaveClick">确定</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import CardBox from 'common/CardBox'
  import VabTable from 'components/VabTable'
  import VabForm from 'components/VabForm'
  import DialogCard from 'common/DialogCard'
  import InsideMarketForm from './InsideMarketForm.vue'
  import { getParamvals } from 'api/tzgl/project/common'
  import { getInsideMarketData, saveInsideMarketData, deleteInsideMarketData } from 'api/tzgl/project/investmentCommitteeExpert';
  import { cloneDeep } from 'lodash'

  export default {
    name:'investmentCommitteeExpert',
    components: {
      CardBox,
      VabTable,
      VabForm,
      DialogCard,
      InsideMarketForm,
    },
    props: {
      activeName: {
        type: String,
        default: '0'
      },
      parentData: {
        type: Object,
        default: ()=>{}
      }
    },
    data(){
      return {
        searchForm: {
          tteType:''
        },
        formItem: [
          {name: 'input', prop: 'zlmName', label: '模糊查询', placeholder: '请输入标签名称'},
          {name: 'input', prop: 'tteRzdw', label: '任职单位', placeholder: '请输入标签名称'},
          {name: 'input', prop: 'tteName', label: '姓名', placeholder: '请输入标签名称'},
          {name: 'button', prop: 'button', label: '查询', type: "primary", click: this.searchTableData},
          {name: 'button', prop: 'button', label: '重置', click: this.handleResetFun},
          {name: 'button', prop: 'button', label: '添加', click: this.handleAddClick},
        ],
        tableHeader: [
          { type: 'index', width:'55', label: "序号",align:'center',fixed: 'left' }, 
          { prop: 'tteName', label:'姓名',align:'center',width:'120' },
          { prop: 'tteAge', label:'年龄',align:'center',width:'120' },
          { prop: 'tteZzmm', label:'政治面貌',align:'center',width:'120' },
          { prop: 'tteTjdw', label:'推荐单位',align:'center',width:'120' },
          { prop: 'tteRzdw', label:'任职单位',align:'center',width:'120' },
          { prop: 'tteYzzw', label:'行政职务',align:'center',width:'120' },
          { prop: 'tteZyjszw', label:'专业技术职务',align:'center',width:'150' },
          { prop: 'tteZyly', label:'专业领域',align:'center',width:'120' },
          { prop: 'tteYwlb', label:'业务类别',align:'center',width:'120' },
          { prop: 'tteYjgny', label:'一级功能域',align:'center',width:'120'  },
          { prop: 'tteEdu', label:'全日制学历',align:'center',width:'120'  },
          { prop: 'tteGraduate', label:'毕业院校',align:'center',width:'120' },
          { prop: 'tteMajor', label:'专业',align:'center',width:'120' },
          { prop: 'tteZzEdu', label:'在职教育',align:'center',width:'120' },
          { prop: 'tteZzGraduate', label:'毕业院校',align:'center',width:'120' },
          { prop: 'tteZzMajor', label:'专业',align:'center',width:'120' },
          { type: 'action', width: '100', label: "操作", align:'center', fixed: 'right', render: (h, scope) => {
            return (
              <div>
                <el-button type="text" size="small" onClick={() => this.handleEdit(scope.row)}>编辑</el-button>
                <el-button type="text" size="small" onClick={() => this.handleDelete(scope.row)}>删除</el-button>
              </div>
            )
          }},
        ],
        tableData: [],
        tableHeight: this.$baseTableHeight(1, 1) - 46, 
        total: 0,
        tablePage: {
          pageNo: 1,
          pageSize: 10,
        },
        dialogVisible: false,
        title:'新增',
        queryForm: {},
        zzmmOptions:[],
        qrzOptions:[],
      }
    },
    created(){
      this.getTableData();
      this.getParamvalsData('政治面貌', 'zzmmOptions')
      this.getParamvalsData('全日制学历', 'qrzOptions')
    },
    methods: {
      async getParamvalsData(lpdName, dataName) {
        const { data } = await getParamvals({ lpdName })
        this[dataName] = data
      },
      async searchTableData() {
        this.getTableData();
      },
      handleResetFun(){
        this.searchForm = {tteType:this.parentData.title};
        this.getTableData();
      },
      async getTableData() {
        this.searchForm.tteType = this.parentData.title;
        const {data:{list,total},code,msg} = await getInsideMarketData(this.searchForm);
        if(code == 200){
          this.tableData = list;
          this.tablePage.total = total;  
        }else{
          this.$message.error(msg);
        }
      },
      // 添加
      handleAddClick(){
        this.dialogVisible = true;
        this.queryForm = {};
        this.title = '新增';
      },
      // 编辑
      handleEdit(row){
        this.dialogVisible = true;
        this.title = '编辑';
        this.queryForm = cloneDeep(row);
      },
      // 保存函数
      handleSaveClick(){
        this.$refs['insideMarketForm'].$refs.form.validate(async (valid) => {
          if (valid) {
            this.$set(this.queryForm, 'tteType', this.parentData.title);
            const {data,code,msg} = await saveInsideMarketData(this.queryForm);
            if(code == 200){
              this.$message.success('保存成功');  
              this.dialogVisible = false;
              this.getTableData();
            }else{
              this.$message.error(msg);
            }
          }
        })
      },
      // 删除
      handleDelete(row){
        this.$baseConfirm('确定删除吗', null, async () => {
          const msg = await deleteInsideMarketData(row)
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            this.getTableData();
          }else{
            this.$message({message:'删除操作失败!',type:'warning'})
          }
        })
      },
      closeDialigCard(){
        this.dialogVisible = false;
      },
      handleSizeChange(val) {
        this.tablePage.pageSize = val
      },
      handleCurrentChange(val) {
        this.tablePage.pageNo = val
      },
    }
  }
</script>

<style lang="sass" scoped>

</style>