<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanListedTargetSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzPlanListedTargetTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="tzPlanListedTargetTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanListedTarget"
      row-key="tplt"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="tzPlanListedTargetEdit">
      <tzPlanListedTargetForm
        ref="tzPlanListedTargetForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="tzPlanListedTargetQuerySearch">
      <tzPlanListedTargetQuery
        ref="tzPlanListedTargetQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { tzPlanListedTargetDoDeleteELog,
           tzPlanListedTargetGetList,
           tzPlanListedTargetDoSaveOrUpdLog,
           tzPlanListedTargetDoExport } from '@/api/tzgl/investDesign/tzPlanListedTarget'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import tzPlanListedTargetSearch from './components/Search.vue'
  import tzPlanListedTargetForm from './components/Form.vue'
  import tzPlanListedTargetQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanListedTarget',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      tzPlanListedTargetSearch,
      tzPlanListedTargetForm,
      tzPlanListedTargetQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tpltDividendRatio: [
            { required: true, message: '请输入现金分红比率', trigger: 'blur' }
          ],
          tpltMarketValue: [
            { required: true, message: '请输入上市公司市值(亿元)', trigger: 'blur' }
          ],
          tpltOdId: [
            { required: true, message: '请输入单位ID', trigger: 'blur' }
          ],
          tpltOeId: [
            { required: true, message: '请输入操作者ID', trigger: 'blur' }
          ],
          tpltOeName: [
            { required: true, message: '请输入操作者', trigger: 'blur' }
          ],
          tpltPriceEps: [
            { required: true, message: '请输入上市公司基本每股收益(EPS)年增长率', trigger: 'blur' }
          ],
          tpltPriceRank: [
            { required: true, message: '请输入市值排名(申万一级行业或恒生综合行业前%)', trigger: 'blur' }
          ],
          tpltPriceRate: [
            { required: true, message: '请输入股价相对综合增长率', trigger: 'blur' }
          ],
          tpltPriceRatio: [
            { required: true, message: '请输入上市公司市净率(是否破净)', trigger: 'blur' }
          ],
          tpltProportion: [
            { required: true, message: '请输入机构投资者占比', trigger: 'blur' }
          ],
          tpltQualityRating: [
            { required: true, message: '请输入证券交易所信息披露质量等级', trigger: 'blur' }
          ],
          tpltStatus: [
            { required: true, message: '请输入状态', trigger: 'blur' }
          ],
          tpltYear: [
            { required: true, message: '请输入年度', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['现金分红比率','上市公司市值(亿元)','单位ID','操作者ID','操作者','上市公司基本每股收益(EPS)年增长率','市值排名(申万一级行业或恒生综合行业前%)','股价相对综合增长率','上市公司市净率(是否破净)','机构投资者占比','证券交易所信息披露质量等级','状态','年度'],
        columns: [
                { prop:'tpltDividendRatio', label:'现金分红比率', width:'auto'   },
                { prop:'tpltMarketValue', label:'上市公司市值(亿元)', width:'auto'   },
                { prop:'tpltOdId', label:'单位ID', width:'auto'   },
                { prop:'tpltOeId', label:'操作者ID', width:'auto'   },
                { prop:'tpltOeName', label:'操作者', width:'auto'   },
                { prop:'tpltPriceEps', label:'上市公司基本每股收益(EPS)年增长率', width:'auto'   },
                { prop:'tpltPriceRank', label:'市值排名(申万一级行业或恒生综合行业前%)', width:'auto'   },
                { prop:'tpltPriceRate', label:'股价相对综合增长率', width:'auto'   },
                { prop:'tpltPriceRatio', label:'上市公司市净率(是否破净)', width:'auto'   },
                { prop:'tpltProportion', label:'机构投资者占比', width:'auto'   },
                { prop:'tpltQualityRating', label:'证券交易所信息披露质量等级', width:'auto'   },
                { prop:'tpltStatus', label:'状态', width:'auto'   },
                { prop:'tpltYear', label:'年度', width:'auto'   }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanListedTargetForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await tzPlanListedTargetDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.tzPlanListedTargetEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={tpltOdId:this.loginUser.odId,tpltOeId:this.loginUser.oeId,tpltOeName:this.loginUser.oeName,tpltStatus:''}
        this.editType = 'add'
        this.$refs['tzPlanListedTargetEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['tzPlanListedTargetEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tplt) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzPlanListedTargetDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['tzPlanListedTargetQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.tzPlanListedTargetQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.tzPlanListedTargetQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await tzPlanListedTargetGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"createdTime":{"celltype":"date"},"updatedTime":{"celltype":"date"}},
                      "fileName":"上市公司市值管理目标表.xls",
                      "isnumber":true,
                      "excelTitle":"上市公司市值管理目标表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#TzPlanListedTarget",params)
        const { msg }  =  await tzPlanListedTargetDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"上市公司市值管理目标表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#TzPlanListedTarget",params)
        const { msg }  =  await tzPlanListedTargetDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>